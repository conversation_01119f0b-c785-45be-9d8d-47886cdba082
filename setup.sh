#!/bin/bash

# 基金投资策略回测计算器 - 快速启动脚本

echo "🚀 基金投资策略回测计算器 - 快速启动"
echo "=================================="

# 检查 Node.js 是否安装
if ! command -v node &> /dev/null; then
    echo "❌ Node.js 未安装，请先安装 Node.js (https://nodejs.org/)"
    exit 1
fi

echo "✅ Node.js 版本: $(node --version)"

# 检查包管理器
if command -v pnpm &> /dev/null; then
    PACKAGE_MANAGER="pnpm"
    echo "✅ 使用 pnpm 作为包管理器"
elif command -v yarn &> /dev/null; then
    PACKAGE_MANAGER="yarn"
    echo "✅ 使用 yarn 作为包管理器"
elif command -v npm &> /dev/null; then
    PACKAGE_MANAGER="npm"
    echo "✅ 使用 npm 作为包管理器"
else
    echo "❌ 未找到包管理器，请安装 npm、yarn 或 pnpm"
    exit 1
fi

# 安装依赖
echo ""
echo "📦 安装项目依赖..."
if [ "$PACKAGE_MANAGER" = "npm" ]; then
    npm install
elif [ "$PACKAGE_MANAGER" = "yarn" ]; then
    yarn install
else
    pnpm install
fi

# 安装额外的依赖包
echo ""
echo "📊 安装图表和工具库..."
if [ "$PACKAGE_MANAGER" = "npm" ]; then
    npm install recharts date-fns react-hook-form
elif [ "$PACKAGE_MANAGER" = "yarn" ]; then
    yarn add recharts date-fns react-hook-form
else
    pnpm add recharts date-fns react-hook-form
fi

echo ""
echo "✅ 安装完成！"
echo ""
echo "🎯 接下来的步骤："
echo "1. 启动开发服务器："
if [ "$PACKAGE_MANAGER" = "npm" ]; then
    echo "   npm run dev"
elif [ "$PACKAGE_MANAGER" = "yarn" ]; then
    echo "   yarn dev"
else
    echo "   pnpm dev"
fi
echo ""
echo "2. 打开浏览器访问: http://localhost:3000"
echo ""
echo "📚 功能说明："
echo "- 选择基金和投资策略"
echo "- 设置策略参数"
echo "- 执行回测分析"
echo "- 查看收益图表和指标"
echo ""
echo "🔧 技术栈："
echo "- Next.js 15 + React 19"
echo "- TypeScript + Tailwind CSS"
echo "- 自定义 SVG 图表"
echo ""
echo "📖 更多信息请查看 README.md"
echo ""

# 询问是否立即启动
read -p "是否立即启动开发服务器？(y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🚀 启动开发服务器..."
    if [ "$PACKAGE_MANAGER" = "npm" ]; then
        npm run dev
    elif [ "$PACKAGE_MANAGER" = "yarn" ]; then
        yarn dev
    else
        pnpm dev
    fi
fi
