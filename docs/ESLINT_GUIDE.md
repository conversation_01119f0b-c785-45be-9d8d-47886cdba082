# ESLint Configuration Guide

## Overview

This project uses a comprehensive ESLint configuration optimized for Next.js 15, React 19, and TypeScript development. The configuration includes multiple plugins and rules to ensure code quality, consistency, and best practices.

## Installed Plugins

### Core Plugins
- **@eslint/js** - JavaScript base rules
- **typescript-eslint** - TypeScript-specific rules and parser
- **eslint-config-next** - Next.js specific configurations

### React & UI Plugins
- **eslint-plugin-react** - React best practices
- **eslint-plugin-react-hooks** - React Hooks rules
- **eslint-plugin-jsx-a11y** - Accessibility guidelines

### Code Quality Plugins
- **eslint-plugin-import-x** - Import/export management
- **eslint-plugin-promise** - Promise/async best practices
- **eslint-plugin-sonarjs** - Code complexity and quality
- **eslint-plugin-unicorn** - Modern JavaScript practices

### Formatting
- **eslint-plugin-prettier** - Code formatting integration
- **eslint-config-prettier** - Disables conflicting ESLint rules

## Available Scripts

```bash
# Run ESLint check
npm run lint

# Run ESLint with auto-fix
npm run lint:fix

# Run ESLint with zero warnings tolerance
npm run lint:check

# Format code with Prettier
npm run format

# Check if code is properly formatted
npm run format:check
```

## Configuration Highlights

### File Ignoring
The configuration automatically ignores:
- `node_modules/`
- `.next/`
- `build/`
- `dist/`
- Coverage reports
- Lock files

### Import Ordering
Imports are automatically organized in this order:
1. React (prioritized)
2. Next.js modules
3. External packages
4. Internal modules (`@/...`)
5. Relative imports

### TypeScript Rules
- Consistent type imports (`import type`)
- Unused variable detection
- Optional chaining preferences
- Interface over type aliases

### React 19 Optimizations
- Prevents leaked renders
- Enforces JSX keys
- Warns about unstable nested components
- Self-closing component consistency

### Next.js Specific Rules
- Allows default exports for pages/layouts
- Optimized for App Router structure
- Middleware support

## Common Issues & Solutions

### 1. Unused Variables
**Error**: `'variable' is defined but never used`
**Solution**: Remove unused imports/variables or prefix with `_`

### 2. Import Ordering
**Error**: `There should be at least one empty line between import groups`
**Solution**: Run `npm run lint:fix` to auto-organize imports

### 3. Type Imports
**Error**: `All imports in the declaration are only used as types`
**Solution**: Use `import type { Type } from 'module'`

### 4. Accessibility Warnings
**Error**: `Visible, non-interactive elements with click handlers...`
**Solution**: Add keyboard event handlers or use proper semantic elements

## Customization

### Disabling Rules
To disable a rule for a specific line:
```javascript
// eslint-disable-next-line rule-name
const problematicCode = something();
```

To disable a rule for an entire file:
```javascript
/* eslint-disable rule-name */
```

### Adjusting Rule Severity
In `eslint.config.mjs`, you can change rule severity:
- `"off"` or `0` - Disable the rule
- `"warn"` or `1` - Warning (doesn't break build)
- `"error"` or `2` - Error (breaks build)

## Performance Tips

1. **Use `.eslintignore`** for large directories
2. **Run incrementally** during development
3. **Use IDE integration** for real-time feedback
4. **Cache results** in CI/CD pipelines

## IDE Integration

### VS Code
Install the ESLint extension and add to settings.json:
```json
{
  "eslint.validate": ["javascript", "typescript", "javascriptreact", "typescriptreact"],
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  }
}
```

### WebStorm
ESLint is automatically detected and enabled.

## Troubleshooting

### Configuration Not Loading
1. Check for syntax errors in `eslint.config.mjs`
2. Ensure all plugins are installed
3. Restart your IDE/development server

### Performance Issues
1. Add more patterns to ignore list
2. Use `--cache` flag for CLI runs
3. Consider using `--max-warnings` in CI

### Plugin Conflicts
1. Check plugin compatibility
2. Review rule overlaps
3. Use `eslint-config-prettier` to resolve formatting conflicts

## Maintenance

### Updating Dependencies
```bash
# Update ESLint and plugins
npm update eslint @typescript-eslint/parser @typescript-eslint/eslint-plugin

# Check for deprecated rules
npm run lint -- --print-config src/app/page.tsx
```

### Adding New Rules
1. Install the plugin: `npm install -D eslint-plugin-name`
2. Add to `eslint.config.mjs`
3. Configure rules as needed
4. Test with existing codebase

## Best Practices

1. **Run ESLint before commits** (use husky + lint-staged)
2. **Fix warnings regularly** to prevent accumulation
3. **Review rule changes** before updating
4. **Document custom rules** for team understanding
5. **Use consistent configuration** across projects

## Resources

- [ESLint Documentation](https://eslint.org/docs/)
- [TypeScript ESLint](https://typescript-eslint.io/)
- [Next.js ESLint](https://nextjs.org/docs/app/api-reference/config/eslint)
- [React ESLint Plugin](https://github.com/jsx-eslint/eslint-plugin-react)
