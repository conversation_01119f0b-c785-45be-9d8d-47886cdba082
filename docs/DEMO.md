# 演示说明

## 快速开始

### 1. 运行快速启动脚本
```bash
./setup.sh
```

### 2. 手动安装（如果脚本不可用）
```bash
# 安装基础依赖
npm install

# 安装图表和工具库
npm install recharts date-fns react-hook-form

# 启动开发服务器
npm run dev
```

## 使用演示

### 步骤 1: 选择基金
在左侧面板中，您可以看到预设的基金列表：
- 易方达沪深300ETF联接A (指数型)
- 华夏中证500ETF联接A (指数型)
- 南方创业板ETF联接A (指数型)
- 兴全合润混合 (混合型)
- 易方达蓝筹精选混合 (混合型)

点击任意基金卡片进行选择。

### 步骤 2: 选择投资策略
选择基金后，可以从以下策略中选择：

1. **定期定额投资** - 适合新手，风险较低
2. **价值平均策略** - 中等风险，需要一定经验
3. **智能定投** - 根据市场估值调整投资
4. **网格交易** - 高风险，适合高级投资者
5. **动量策略** - 追踪趋势，高风险
6. **均值回归策略** - 基于价格偏离均值

### 步骤 3: 设置参数
根据选择的策略，系统会动态显示相应的参数输入框：

#### 定投策略参数示例：
- 开始日期: 2020-01-01
- 结束日期: 2024-01-01
- 初始投资金额: 10,000 元
- 定投金额: 1,000 元
- 投资频率: 每月

#### 价值平均策略参数示例：
- 开始日期: 2020-01-01
- 结束日期: 2024-01-01
- 初始投资金额: 10,000 元
- 目标年化增长率: 8%
- 单次最大投资额: 5,000 元
- 调整频率: 每月

### 步骤 4: 执行回测
填写完所有必填参数后，点击"开始回测"按钮。系统将：
1. 生成模拟的基金历史数据
2. 根据策略计算投资时间线
3. 计算各项风险收益指标
4. 生成可视化图表

### 步骤 5: 分析结果
回测完成后，您可以看到：

#### 关键指标
- **总收益率**: 整个投资期间的总收益
- **年化收益率**: 按年计算的平均收益
- **最大回撤**: 投资期间的最大亏损幅度
- **夏普比率**: 风险调整后的收益指标

#### 可视化图表
- 投资收益走势图
- 累计投入 vs 资产价值对比
- 投资明细表格

## 模拟数据说明

当前版本使用模拟数据进行演示：

### 基金净值模拟
- 基于随机游走模型生成
- 考虑不同基金类型的波动特性
- 包含市场事件的随机冲击

### 指数数据模拟
- 生成对应的指数价格、PE、PB 数据
- 用于智能定投策略的估值判断

### 数据特点
- 跳过周末和节假日
- 包含合理的价格波动
- 模拟真实市场的趋势和波动

## 扩展示例

### 添加新基金
在 `src/lib/mockData.ts` 中添加：

```typescript
{
  id: 'fund_006',
  name: '新基金名称',
  code: '123456',
  type: 'equity',
  riskLevel: 'medium',
  indexId: 'hs300'
}
```

### 添加新策略
1. 在 `src/types/fund.ts` 中定义参数类型
2. 在 `src/lib/strategies.ts` 中添加策略配置
3. 在 `src/lib/backtest.ts` 中实现计算逻辑

### 接入真实数据
项目已集成真实的基金数据API：
- 使用百度股市通API获取基金净值数据
- 支持基金代码验证和基本信息获取
- 可通过环境变量切换真实数据和模拟数据

## 常见问题

### Q: 为什么回测结果每次都不同？
A: 当前使用随机生成的模拟数据，每次运行会产生不同的价格序列。在实际应用中应使用真实的历史数据。

### Q: 如何添加更多投资策略？
A: 参考现有策略的实现方式，在相应文件中添加策略定义、参数配置和计算逻辑。

### Q: 可以导出回测结果吗？
A: 当前版本暂不支持导出功能，这是计划中的扩展功能。

### Q: 支持移动端吗？
A: 使用了响应式设计，支持移动端访问，但最佳体验仍在桌面端。

## 技术细节

### 回测引擎
- 基于时间序列的投资模拟
- 支持多种投资频率（周、月、季度）
- 计算标准的风险收益指标

### 图表渲染
- 使用 SVG 绘制自定义图表
- 支持缩放和交互
- 可扩展为 Recharts 等专业图表库

### 性能优化
- 使用 React 19 的最新特性
- 服务端渲染提升首屏加载速度
- 组件级别的状态管理

## 下一步计划

1. **数据接入**: 集成真实的基金数据源
2. **策略扩展**: 添加更多专业投资策略
3. **功能增强**: 策略对比、报告导出等
4. **用户系统**: 账户管理、历史记录等
5. **移动应用**: 开发原生移动端应用
