# 网格交易策略实现文档

## 概述

网格交易是一种在价格区间内设置多个买卖点的投资策略，通过低买高卖获取价差收益。本文档详细介绍了网格交易策略在基金投资回测系统中的实现。

## 策略原理

### 基本概念

1. **价格区间**: 设定一个预期的价格波动范围（最低价和最高价）
2. **网格数量**: 在价格区间内等分设置的交易点数量
3. **网格间距**: 相邻两个网格之间的价格差
4. **每格投资额**: 每个网格点的固定投资金额

### 交易逻辑

```
价格区间 = [最低价, 最高价]
网格间距 = (最高价 - 最低价) / 网格数量

当价格下跌到某个网格点时 → 买入
当价格上涨超过网格点一定幅度时 → 卖出（减少投资）
```

## 实现架构

### 1. 数据结构定义

```typescript
// 网格交易策略参数
export interface GridTradingParams extends BaseStrategyParams {
  gridCount: number;                    // 网格数量
  priceRange: {                        // 价格区间
    min: number;
    max: number;
  };
  investmentPerGrid: number;           // 每格投资额
  rebalanceFrequency: "daily" | "weekly" | "monthly"; // 调仓频率
  stopLoss?: number;                   // 止损比例 (%)
  takeProfit?: number;                 // 止盈比例 (%)
}
```

### 2. 核心算法实现

#### 价格区间计算
```typescript
private extractPriceRange(params: GridTradingParams): { min: number; max: number } {
  // 1. 优先使用用户设定的价格区间
  if ('priceRange' in params && params.priceRange) {
    return params.priceRange;
  }
  
  // 2. 从策略参数中提取
  const paramsAny = params as any;
  if (paramsAny.priceRangeMin && paramsAny.priceRangeMax) {
    return {
      min: paramsAny.priceRangeMin,
      max: paramsAny.priceRangeMax
    };
  }

  // 3. 使用历史数据自动计算
  return this.calculatePriceRangeFromHistory();
}
```

#### 网格位置计算
```typescript
// 计算网格间距
const gridSpacing = (priceRange.max - priceRange.min) / params.gridCount;

// 计算当前价格所在的网格位置
const gridPosition = Math.floor((currentPrice - priceRange.min) / gridSpacing);

// 计算目标网格价格
const targetGridPrice = priceRange.min + (gridPosition + 0.5) * gridSpacing;
```

#### 交易决策逻辑
```typescript
if (currentPrice < targetGridPrice) {
  // 价格较低，增加投资
  const investmentAmount = Math.min(
    params.investmentPerGrid,
    gridState.availableCash
  );
  return investmentAmount;
} else if (currentPrice > targetGridPrice * 1.1) {
  // 价格较高，减少投资
  return 0;
}
```

### 3. 调仓频率控制

```typescript
private shouldRebalance(
  params: GridTradingParams,
  timeline: BacktestResult["timeline"]
): boolean {
  switch (params.rebalanceFrequency) {
    case 'daily':
      return true; // 每次都检查
    case 'weekly':
      // 检查是否过了一周
      const weeksDiff = Math.floor((currentDate.getTime() - lastDate.getTime()) / (7 * 24 * 60 * 60 * 1000));
      return weeksDiff >= 1;
    case 'monthly':
      // 检查是否过了一个月
      return currentDate.getMonth() !== lastDate.getMonth();
  }
}
```

## 数据源集成

### 基金数据获取

支持真实数据和模拟数据两种模式：

```typescript
// 真实数据：使用百度股市通API
const realData = await fetchFundData(fundCode, startDate, endDate);

// 模拟数据：算法生成
const mockData = generateMockFundData(fundId, startDate, endDate);
```

### 指数数据获取

新增指数数据API支持：

```typescript
// 指数数据API
const indexData = await fetchIndexData(indexCode, startDate, endDate);

// API URL构建
const url = 'https://finance.pae.baidu.com/vapi/v1/getquotation';
```

## 用户界面

### 参数配置界面

1. **基础参数**
   - 开始日期 / 结束日期
   - 初始投资金额

2. **网格设置**
   - 网格数量（5-50个）
   - 价格区间上下限
   - 每格投资额

3. **交易设置**
   - 调仓频率（每日/每周/每月）
   - 止损比例（可选）
   - 止盈比例（可选）

### 结果展示

1. **关键指标**
   - 总收益率
   - 年化收益率
   - 最大回撤
   - 夏普比率

2. **投资明细**
   - 每次交易的详细记录
   - 持仓变化情况
   - 收益率变化趋势

## 使用示例

### 基本用法

```typescript
const gridParams: GridTradingParams = {
  startDate: '2023-01-01',
  endDate: '2024-01-01',
  initialAmount: 50000,
  gridCount: 10,
  priceRange: { min: 1.0, max: 3.0 },
  investmentPerGrid: 2000,
  rebalanceFrequency: 'weekly'
};

const engine = new BacktestEngine(fundData, indexData);
const result = await engine.runBacktest(fund, gridParams);
```

### 演示页面

访问应用主页，点击"网格交易"标签页可以：

1. 调整网格交易参数
2. 执行回测分析
3. 查看详细结果
4. 分析策略表现

## 优化建议

### 1. 动态网格调整

- 根据市场波动率动态调整网格间距
- 支持非等距网格设置
- 增加趋势判断逻辑

### 2. 风险控制

- 完善止损止盈机制
- 增加最大持仓限制
- 添加资金管理规则

### 3. 性能优化

- 优化大量数据的计算性能
- 增加并行计算支持
- 实现增量计算逻辑

### 4. 用户体验

- 增加可视化网格图表
- 提供策略模板
- 添加参数优化建议

## 注意事项

1. **价格区间设置**: 需要根据历史数据和市场预期合理设置
2. **网格数量**: 过多会增加交易成本，过少可能错失机会
3. **调仓频率**: 需要平衡收益和交易成本
4. **资金管理**: 确保有足够资金支持网格交易
5. **市场适应性**: 网格交易更适合震荡市场，趋势市场效果有限

## 扩展功能

### 计划中的功能

- [ ] 多基金网格交易组合
- [ ] 智能网格间距调整
- [ ] 实时交易信号提醒
- [ ] 网格交易可视化图表
- [ ] 策略参数优化算法
- [ ] 风险评估和预警

### 技术改进

- [ ] 支持更多数据源
- [ ] 增加机器学习预测
- [ ] 实现实时数据更新
- [ ] 优化计算性能
- [ ] 增加单元测试覆盖

## 总结

网格交易策略的实现为基金投资回测系统增加了一个重要的高级策略选项。通过合理的参数设置和风险控制，可以在震荡市场中获得稳定的收益。

该实现具有以下特点：
- 完整的策略逻辑
- 灵活的参数配置
- 真实数据支持
- 直观的结果展示
- 良好的扩展性

用户可以通过调整不同的参数组合，找到适合自己风险偏好和市场预期的网格交易策略。
