# CSRC Fund API Guide

## Overview

The China Securities Regulatory Commission (CSRC) provides a public API to access fund net value information. This API returns comprehensive data about mutual funds, including net asset values, fund names, codes, and valuation dates.

**Base URL:** `http://eid.csrc.gov.cn/fund/disclose/getPublicFundJZInfoMore.do`

## API Structure

The API uses a complex parameter structure called `aoData` which is a JSON array encoded as a URL parameter. Each element in the array has a `name` and `value` property.

### Key Parameters

| Parameter | Description | Example |
|-----------|-------------|---------|
| `sEcho` | Request sequence number | `1` |
| `iDisplayStart` | Starting record index (0-based) | `0`, `20`, `100` |
| `iDisplayLength` | Number of records to return | `20`, `50`, `100` |
| `fundCode` | Specific fund code to search | `"110003"`, `"007070"` |
| `fundName` | Fund name partial match | `"沪深300"`, `"ETF"`, `"债券"` |
| `fundCompanyShortName` | Fund company name | `"华夏"`, `"博时"` |
| `startDate` | Start date (YYYY-MM-DD) | `"2024-12-31"` |
| `endDate` | End date (YYYY-MM-DD) | `"2024-12-31"` |
| `fundType` | Fund type filter | `"all"` |

## Response Format

```json
{
  "iTotalRecords": 30846,
  "aaData": [
    {
      "code": "007070",
      "shortName": "博时颐泽稳健养老目标一年持有混合（FOF）A",
      "shareNetValue": "1.1380",
      "totalNetValue": "1.1380",
      "valuationDate": "2024-12-31",
      "classification": {...},
      "fund": {...}
    }
  ]
}
```

## Key Findings

### 1. Date Sensitivity
- **Critical:** The API requires explicit date ranges (`startDate` and `endDate`)
- Without dates, most searches return 0 results
- Use recent trading days (avoid weekends and holidays)
- Format: `YYYY-MM-DD`

### 2. Pagination
- Use `iDisplayStart` for pagination (0-based index)
- Use `iDisplayLength` to control page size
- Maximum practical page size appears to be around 100 records

### 3. Search Capabilities
- **Fund Code:** Exact match search
- **Fund Name:** Supports partial matching (Chinese characters)
- **Company Name:** Search by fund management company

### 4. Data Characteristics
- Total records available: ~30,000+ funds
- Many funds have multiple share classes (A, C, Y)
- Some funds may not have net values on all dates
- Response includes both active and inactive funds

## Usage Examples

### Basic Fund Search
```javascript
// Get all funds for a specific date
const result = await fetchFundData({
  startDate: '2024-12-31',
  endDate: '2024-12-31',
  iDisplayLength: 20
});
```

### Search by Fund Name
```javascript
// Search for ETF funds
const etfFunds = await fetchFundData({
  fundName: 'ETF',
  startDate: '2024-12-31',
  endDate: '2024-12-31',
  iDisplayLength: 50
});
```

### Get Specific Fund
```javascript
// Get specific fund by code
const fund = await fetchFundData({
  fundCode: '110003',
  startDate: '2024-12-31',
  endDate: '2024-12-31'
});
```

### Pagination
```javascript
// Get second page of results
const page2 = await fetchFundData({
  startDate: '2024-12-31',
  endDate: '2024-12-31',
  iDisplayStart: 20,
  iDisplayLength: 20
});
```

## Common Search Terms

| Term | Description | Typical Results |
|------|-------------|-----------------|
| `"ETF"` | Exchange Traded Funds | ~500+ funds |
| `"指数"` | Index funds | ~1000+ funds |
| `"债券"` | Bond funds | ~3000+ funds |
| `"混合"` | Hybrid funds | ~12000+ funds |
| `"FOF"` | Fund of Funds | ~1300+ funds |
| `"养老"` | Pension funds | ~700+ funds |

## Best Practices

### 1. Rate Limiting
- Add delays between requests (1-2 seconds)
- Don't make too many concurrent requests
- The API may timeout on complex searches

### 2. Error Handling
- Handle timeout errors gracefully
- Some searches may return 0 results even with valid parameters
- Network issues are common with this API

### 3. Data Processing
- Filter out funds without net values if needed
- Handle multiple share classes appropriately
- Validate dates before making requests

### 4. Performance Tips
- Use specific date ranges rather than broad searches
- Start with smaller page sizes for testing
- Cache results when possible

## Limitations

1. **Date Dependency:** Most functionality requires explicit dates
2. **Timeout Issues:** Complex searches may timeout
3. **No Historical Range:** Can only query single dates, not date ranges
4. **Limited Filtering:** No advanced filtering options
5. **Rate Limits:** Unofficial but apparent rate limiting

## Integration with Your Project

The provided utility functions in `src/utils/csrc-fund-api.js` offer:

- `fetchFundData(params)` - Raw API access
- `searchFundsByName(name, limit)` - Search by fund name
- `getFundByCode(code, date)` - Get specific fund
- `getAllFunds(page, pageSize, date)` - Paginated fund list
- `getFundsByCompany(company, limit)` - Search by company

## Sample Data Structure

```javascript
{
  code: "007070",
  name: "博时颐泽稳健养老目标一年持有混合（FOF）A",
  shareNetValue: "1.1380",
  totalNetValue: "1.1380", 
  valuationDate: "2024-12-31"
}
```

This API provides comprehensive access to Chinese mutual fund data and can be valuable for fund analysis, comparison, and strategy development.
