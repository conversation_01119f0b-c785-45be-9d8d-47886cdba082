import { dirname } from "path";
import { fileURLToPath } from "url";
import { FlatCompat } from "@eslint/eslintrc";
import js from "@eslint/js";
import tseslint from "typescript-eslint";
import reactPlugin from "eslint-plugin-react";
import reactHooksPlugin from "eslint-plugin-react-hooks";
import jsxA11yPlugin from "eslint-plugin-jsx-a11y";
import importPlugin from "eslint-plugin-import-x";
import promisePlugin from "eslint-plugin-promise";
import sonarjsPlugin from "eslint-plugin-sonarjs";
import unicornPlugin from "eslint-plugin-unicorn";
import prettierPlugin from "eslint-plugin-prettier";
import prettierConfig from "eslint-config-prettier";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const compat = new FlatCompat({
  baseDirectory: __dirname,
});

const eslintConfig = [
  // Base configurations
  js.configs.recommended,
  ...tseslint.configs.recommended,
  ...compat.extends("next/core-web-vitals", "next/typescript"),

  // Global settings
  {
    languageOptions: {
      ecmaVersion: "latest",
      sourceType: "module",
      parserOptions: {
        ecmaFeatures: {
          jsx: true,
        },
      },
    },
    settings: {
      react: {
        version: "detect",
      },
      "import-x/resolver": {
        typescript: {
          alwaysTryTypes: true,
          project: "./tsconfig.json",
        },
        node: true,
      },
    },
  },

  // Ignore patterns for better performance
  {
    ignores: [
      "**/node_modules/**",
      "**/.next/**",
      "**/out/**",
      "**/build/**",
      "**/dist/**",
      "**/.vercel/**",
      "**/coverage/**",
      "**/*.min.js",
      "**/public/**/*.js",
      "**/.env*",
      "**/pnpm-lock.yaml",
      "**/package-lock.json",
      "**/yarn.lock",
    ],
  },

  // React configuration
  {
    files: ["**/*.{js,jsx,ts,tsx}"],
    plugins: {
      react: reactPlugin,
      "react-hooks": reactHooksPlugin,
    },
    rules: {
      ...reactPlugin.configs.recommended.rules,
      ...reactHooksPlugin.configs.recommended.rules,
      "react/react-in-jsx-scope": "off", // Not needed in Next.js
      "react/prop-types": "off", // Using TypeScript
      "react/jsx-uses-react": "off", // Not needed in React 17+
      "react/jsx-uses-vars": "error",
      // React 19 specific optimizations
      "react/jsx-no-leaked-render": "warn", // Prevent leaked renders (warn instead of error)
      "react/jsx-key": ["error", { checkFragmentShorthand: true }], // Ensure keys in fragments
      "react/no-unstable-nested-components": "warn", // Prevent performance issues (warn)
      "react/jsx-no-useless-fragment": ["warn", { allowExpressions: true }], // Clean JSX (warn)
      "react/self-closing-comp": "warn", // Consistent self-closing tags (warn)
      "react/jsx-boolean-value": "off", // Allow flexible boolean props
      "react/jsx-curly-brace-presence": "off", // Allow flexible JSX formatting
    },
  },

  // JSX Accessibility
  {
    files: ["**/*.{js,jsx,ts,tsx}"],
    plugins: {
      "jsx-a11y": jsxA11yPlugin,
    },
    rules: {
      ...jsxA11yPlugin.configs.recommended.rules,
      "jsx-a11y/click-events-have-key-events": "warn", // Warn instead of error
      "jsx-a11y/no-static-element-interactions": "warn", // Warn instead of error
    },
  },

  // Import rules
  {
    files: ["**/*.{js,jsx,ts,tsx}"],
    plugins: {
      "import-x": importPlugin,
    },
    rules: {
      ...importPlugin.configs.recommended.rules,
      ...importPlugin.configs.typescript.rules,
      "import-x/order": [
        "error",
        {
          groups: [
            "builtin",
            "external",
            "internal",
            "parent",
            "sibling",
            "index",
          ],
          "newlines-between": "always",
          alphabetize: {
            order: "asc",
            caseInsensitive: true,
          },
          pathGroups: [
            {
              pattern: "react",
              group: "external",
              position: "before",
            },
            {
              pattern: "next/**",
              group: "external",
              position: "before",
            },
            {
              pattern: "@/**",
              group: "internal",
              position: "before",
            },
          ],
          pathGroupsExcludedImportTypes: ["react", "next"],
        },
      ],
      "import-x/no-unresolved": "error",
      "import-x/no-unused-modules": "warn",
      "import-x/no-default-export": "off", // Next.js uses default exports for pages
      "import-x/prefer-default-export": "off", // Allow named exports
      "import-x/no-cycle": "error", // Prevent circular dependencies
      "import-x/no-self-import": "error", // Prevent self imports
    },
  },

  // Promise rules
  {
    files: ["**/*.{js,jsx,ts,tsx}"],
    plugins: {
      promise: promisePlugin,
    },
    rules: {
      ...promisePlugin.configs.recommended.rules,
    },
  },

  // SonarJS rules
  {
    files: ["**/*.{js,jsx,ts,tsx}"],
    plugins: {
      sonarjs: sonarjsPlugin,
    },
    rules: {
      ...sonarjsPlugin.configs.recommended.rules,
      "sonarjs/cognitive-complexity": ["error", 20], // Increase complexity threshold
      "sonarjs/unused-import": "off", // Let TypeScript handle this
      "sonarjs/prefer-read-only-props": "off", // Too strict for React
      "sonarjs/different-types-comparison": "off", // Allow flexible comparisons
      "sonarjs/pseudo-random": "off", // Allow Math.random
      "sonarjs/deprecation": "off", // Allow deprecated APIs for compatibility
    },
  },

  // Unicorn rules
  {
    files: ["**/*.{js,jsx,ts,tsx}"],
    plugins: {
      unicorn: unicornPlugin,
    },
    rules: {
      ...unicornPlugin.configs.recommended.rules,
      "unicorn/prevent-abbreviations": "off", // Too strict for React props
      "unicorn/filename-case": [
        "error",
        {
          cases: {
            camelCase: true,
            pascalCase: true,
            kebabCase: true,
          },
        },
      ],
      "unicorn/no-null": "off", // React uses null
      "unicorn/prefer-number-properties": "off", // Allow isNaN for compatibility
      "unicorn/numeric-separators-style": "off", // Allow flexible number formatting
      "unicorn/catch-error-name": "off", // Allow flexible error naming
      "unicorn/consistent-function-scoping": "off", // Allow nested functions
      "unicorn/no-array-for-each": "off", // Allow forEach
      "unicorn/prefer-dom-node-append": "off", // Allow appendChild
      "unicorn/prefer-dom-node-remove": "off", // Allow removeChild
      "unicorn/prefer-string-slice": "off", // Allow substr
      "unicorn/no-instanceof-builtins": "off", // Allow instanceof
      "unicorn/switch-case-braces": "off", // Allow switch without braces
      "unicorn/new-for-builtins": "off", // Allow Array() constructor
    },
  },

  // Prettier integration (uncomment when ready to enforce formatting)
  {
    files: ["**/*.{js,jsx,ts,tsx}"],
    plugins: {
      prettier: prettierPlugin,
    },
    rules: {
      ...prettierPlugin.configs.recommended.rules,
      "prettier/prettier": "warn", // Use warn instead of error for less disruption
    },
  },

  // Disable conflicting rules with Prettier (must be last)
  prettierConfig,

  // TypeScript specific overrides
  {
    files: ["**/*.{ts,tsx}"],
    languageOptions: {
      parser: tseslint.parser,
      parserOptions: {
        project: "./tsconfig.json",
        tsconfigRootDir: __dirname,
      },
    },
    rules: {
      "@typescript-eslint/no-unused-vars": [
        "error",
        {
          argsIgnorePattern: "^_",
          varsIgnorePattern: "^_",
          destructuredArrayIgnorePattern: "^_",
        },
      ],
      "@typescript-eslint/explicit-function-return-type": "off",
      "@typescript-eslint/explicit-module-boundary-types": "off",
      "@typescript-eslint/no-explicit-any": "warn",
      "@typescript-eslint/no-var-requires": "error",
      "@typescript-eslint/consistent-type-imports": [
        "error",
        { prefer: "type-imports", fixStyle: "separate-type-imports" },
      ],
      "@typescript-eslint/consistent-type-definitions": ["error", "interface"],
      "@typescript-eslint/prefer-optional-chain": "error",
    },
  },

  // Configuration files
  {
    files: ["*.config.{js,ts,mjs}", "*.setup.{js,ts}"],
    rules: {
      "import-x/no-default-export": "off",
      "unicorn/prefer-module": "off",
    },
  },

  // Test files
  {
    files: ["**/*.test.{js,jsx,ts,tsx}", "**/*.spec.{js,jsx,ts,tsx}"],
    rules: {
      "sonarjs/no-duplicate-string": "off",
      "@typescript-eslint/no-explicit-any": "off",
      "@typescript-eslint/no-floating-promises": "off",
      "unicorn/consistent-function-scoping": "off",
    },
  },

  // Next.js specific files
  {
    files: [
      "src/app/**/page.{js,jsx,ts,tsx}",
      "src/app/**/layout.{js,jsx,ts,tsx}",
      "src/app/**/loading.{js,jsx,ts,tsx}",
      "src/app/**/error.{js,jsx,ts,tsx}",
      "src/app/**/not-found.{js,jsx,ts,tsx}",
      "src/pages/**/*.{js,jsx,ts,tsx}",
      "src/middleware.{js,ts}",
    ],
    rules: {
      "import-x/no-default-export": "off", // Next.js requires default exports
      "import-x/prefer-default-export": "error",
    },
  },

  // Data visualization and chart components (Recharts specific)
  {
    files: ["**/components/**/*Chart*.{js,jsx,ts,tsx}", "**/components/**/*Graph*.{js,jsx,ts,tsx}"],
    rules: {
      "react/jsx-props-no-spreading": "off", // Recharts often uses prop spreading
      "@typescript-eslint/no-explicit-any": "off", // Chart data can be flexible
      "unicorn/no-null": "off", // Charts may use null for missing data
    },
  },
];

export default eslintConfig;
