"use client";

import type { BacktestResult } from "@/types/fund";
import PerformanceComparisonChart from "./PerformanceComparisonChart";

interface BacktestChartProps {
  result: BacktestResult;
}

export default function BacktestChart({ result }: BacktestChartProps) {
  const { performance, timeline } = result;

  // 格式化数字显示
  const formatNumber = (num: number, decimals: number = 2): string => {
    return num.toLocaleString("zh-CN", {
      minimumFractionDigits: decimals,
      maximumFractionDigits: decimals,
    });
  };

  // 格式化百分比
  const formatPercent = (num: number): string => {
    return `${formatNumber(num)}%`;
  };

  // 格式化金额
  const formatCurrency = (num: number): string => {
    return `¥${formatNumber(num)}`;
  };

  // 获取收益率颜色
  const getReturnColor = (value: number): string => {
    if (value > 0) return "text-green-600";
    if (value < 0) return "text-red-600";
    return "text-gray-600";
  };

  // 简化的图表组件（SVG实现）
  const SimpleChart = () => {
    if (timeline.length === 0) return null;

    const width = 800;
    const height = 400;
    const padding = 60;
    const chartWidth = width - 2 * padding;
    const chartHeight = height - 2 * padding;

    // 计算数据范围
    const maxValue = Math.max(
      ...timeline.map((d) => Math.max(d.value, d.totalInvestment))
    );
    const minValue = Math.min(
      ...timeline.map((d) => Math.min(d.value, d.totalInvestment))
    );
    const valueRange = maxValue - minValue;

    // 生成路径点
    const generatePath = (data: number[]) => {
      return data
        .map((value, index) => {
          const x = padding + (index / (data.length - 1)) * chartWidth;
          const y = padding + ((maxValue - value) / valueRange) * chartHeight;
          return `${index === 0 ? "M" : "L"} ${x} ${y}`;
        })
        .join(" ");
    };

    const valuePath = generatePath(timeline.map((d) => d.value));
    const investmentPath = generatePath(timeline.map((d) => d.totalInvestment));

    return (
      <div className="w-full overflow-x-auto">
        <svg
          width={width}
          height={height}
          className="border border-gray-200 rounded-lg"
        >
          {/* 网格线 */}
          <defs>
            <pattern
              id="grid"
              width="40"
              height="40"
              patternUnits="userSpaceOnUse"
            >
              <path
                d="M 40 0 L 0 0 0 40"
                fill="none"
                stroke="#f3f4f6"
                strokeWidth="1"
              />
            </pattern>
          </defs>
          <rect width={width} height={height} fill="url(#grid)" />

          {/* Y轴标签 */}
          {[0, 0.25, 0.5, 0.75, 1].map((ratio) => {
            const y = padding + ratio * chartHeight;
            const value = maxValue - ratio * valueRange;
            return (
              <g key={ratio}>
                <line
                  x1={padding - 5}
                  y1={y}
                  x2={padding}
                  y2={y}
                  stroke="#6b7280"
                  strokeWidth="1"
                />
                <text
                  x={padding - 10}
                  y={y + 4}
                  textAnchor="end"
                  fontSize="12"
                  fill="#6b7280"
                >
                  {formatCurrency(value)}
                </text>
              </g>
            );
          })}

          {/* X轴标签 */}
          {timeline
            .filter((_, index) => index % Math.ceil(timeline.length / 6) === 0)
            .map((entry, index) => {
              const x =
                padding +
                (timeline.indexOf(entry) / (timeline.length - 1)) * chartWidth;
              return (
                <g key={index}>
                  <line
                    x1={x}
                    y1={height - padding}
                    x2={x}
                    y2={height - padding + 5}
                    stroke="#6b7280"
                    strokeWidth="1"
                  />
                  <text
                    x={x}
                    y={height - padding + 20}
                    textAnchor="middle"
                    fontSize="12"
                    fill="#6b7280"
                  >
                    {new Date(entry.date).toLocaleDateString("zh-CN", {
                      month: "short",
                      year: "2-digit",
                    })}
                  </text>
                </g>
              );
            })}

          {/* 投资金额线 */}
          <path
            d={investmentPath}
            fill="none"
            stroke="#94a3b8"
            strokeWidth="2"
            strokeDasharray="5,5"
          />

          {/* 资产价值线 */}
          <path d={valuePath} fill="none" stroke="#3b82f6" strokeWidth="3" />

          {/* 图例 */}
          <g transform={`translate(${width - 150}, 30)`}>
            <rect
              x="0"
              y="0"
              width="140"
              height="50"
              fill="white"
              stroke="#e5e7eb"
              strokeWidth="1"
              rx="4"
            />
            <line
              x1="10"
              y1="15"
              x2="30"
              y2="15"
              stroke="#3b82f6"
              strokeWidth="3"
            />
            <text x="35" y="19" fontSize="12" fill="#374151">
              资产价值
            </text>
            <line
              x1="10"
              y1="35"
              x2="30"
              y2="35"
              stroke="#94a3b8"
              strokeWidth="2"
              strokeDasharray="3,3"
            />
            <text x="35" y="39" fontSize="12" fill="#374151">
              累计投入
            </text>
          </g>
        </svg>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      <h3 className="text-lg font-semibold text-gray-900">回测结果</h3>

      {/* 关键指标 */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div className="bg-white p-4 border border-gray-200 rounded-lg">
          <div className="text-sm text-gray-600 mb-1">总收益率</div>
          <div
            className={`text-xl font-bold ${getReturnColor(performance.totalReturn)}`}
          >
            {formatPercent(performance.totalReturn)}
          </div>
        </div>

        <div className="bg-white p-4 border border-gray-200 rounded-lg">
          <div className="text-sm text-gray-600 mb-1">年化收益率</div>
          <div
            className={`text-xl font-bold ${getReturnColor(performance.annualizedReturn)}`}
          >
            {formatPercent(performance.annualizedReturn)}
          </div>
        </div>

        <div className="bg-white p-4 border border-gray-200 rounded-lg">
          <div className="text-sm text-gray-600 mb-1">最大回撤</div>
          <div className="text-xl font-bold text-red-600">
            {formatPercent(performance.maxDrawdown)}
          </div>
        </div>

        <div className="bg-white p-4 border border-gray-200 rounded-lg">
          <div className="text-sm text-gray-600 mb-1">夏普比率</div>
          <div className="text-xl font-bold text-gray-900">
            {formatNumber(performance.sharpeRatio)}
          </div>
        </div>
      </div>

      {/* 详细指标 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-white p-4 border border-gray-200 rounded-lg">
          <div className="text-sm text-gray-600 mb-1">累计投入</div>
          <div className="text-lg font-semibold text-gray-900">
            {formatCurrency(performance.totalInvestment)}
          </div>
        </div>

        <div className="bg-white p-4 border border-gray-200 rounded-lg">
          <div className="text-sm text-gray-600 mb-1">最终价值</div>
          <div className="text-lg font-semibold text-gray-900">
            {formatCurrency(performance.finalValue)}
          </div>
        </div>

        <div className="bg-white p-4 border border-gray-200 rounded-lg">
          <div className="text-sm text-gray-600 mb-1">波动率</div>
          <div className="text-lg font-semibold text-gray-900">
            {formatPercent(performance.volatility)}
          </div>
        </div>
      </div>

      {/* 收益率对比图表 */}
      <PerformanceComparisonChart result={result} />

      {/* 投资价值走势图 */}
      <div className="bg-white p-6 border border-gray-200 rounded-lg">
        <h4 className="text-md font-semibold text-gray-900 mb-4">
          投资价值走势图
        </h4>
        <SimpleChart />
      </div>

      {/* 投资明细表格 */}
      <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h4 className="text-md font-semibold text-gray-900">投资明细</h4>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  日期
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  投入金额
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  累计投入
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  持有份额
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  当前价值
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  收益率
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {timeline.slice(-10).map((entry, index) => (
                <tr
                  key={index}
                  className={index % 2 === 0 ? "bg-white" : "bg-gray-50"}
                >
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {new Date(entry.date).toLocaleDateString("zh-CN")}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {formatCurrency(entry.investment)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {formatCurrency(entry.totalInvestment)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {formatNumber(entry.shares)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {formatCurrency(entry.value)}
                  </td>
                  <td
                    className={`px-6 py-4 whitespace-nowrap text-sm font-medium ${getReturnColor(entry.return)}`}
                  >
                    {formatPercent(entry.return)}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        {timeline.length > 10 && (
          <div className="px-6 py-3 bg-gray-50 text-sm text-gray-500 text-center">
            显示最近10条记录，共{timeline.length}条
          </div>
        )}
      </div>
    </div>
  );
}
