// API接口测试工具

import type { BaiduApiResponse } from './types';
import { buildApiUrl, validateApiParams, parseFundData, parsePerformanceData } from './utils';

/**
 * API测试结果接口
 */
export interface ApiTestResult {
  url: string;
  success: boolean;
  responseTime: number;
  dataPoints: number;
  error?: string;
  sampleData?: any;
}

/**
 * 批量测试结果接口
 */
export interface BatchTestResult {
  fundCode: string;
  tests: {
    netValue: ApiTestResult;
    performance3m: ApiTestResult;
    performance6m: ApiTestResult;
    performance12m: ApiTestResult;
    longTerm: ApiTestResult;
  };
  summary: {
    totalTests: number;
    successCount: number;
    failureCount: number;
    averageResponseTime: number;
  };
}

/**
 * API接口测试器类
 */
export class ApiTester {
  private timeout: number = 10000;

  /**
   * 测试单个API接口
   */
  async testSingleApi(params: {
    fundCode: string;
    dataType?: 'nvl' | 'ai';
    months?: number;
    source?: string;
  }): Promise<ApiTestResult> {
    const startTime = Date.now();
    
    try {
      // 验证参数
      const validation = validateApiParams(params);
      if (!validation.valid) {
        return {
          url: '',
          success: false,
          responseTime: 0,
          dataPoints: 0,
          error: `参数验证失败: ${validation.errors.join(', ')}`
        };
      }

      // 构建URL
      const url = buildApiUrl(params.fundCode, {
        dataType: params.dataType,
        months: params.months,
        source: params.source
      });

      // 发送请求
      const response = await Promise.race([
        fetch(url, {
          method: 'GET',
          headers: {
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
          }
        }),
        new Promise<never>((_, reject) => 
          setTimeout(() => reject(new Error('请求超时')), this.timeout)
        )
      ]);

      const responseTime = Date.now() - startTime;

      if (!response.ok) {
        return {
          url,
          success: false,
          responseTime,
          dataPoints: 0,
          error: `HTTP ${response.status}: ${response.statusText}`
        };
      }

      const data: BaiduApiResponse = await response.json();

      if (data.ResultCode !== '0') {
        return {
          url,
          success: false,
          responseTime,
          dataPoints: 0,
          error: `API错误: ${data.ResultCode}`
        };
      }

      // 解析数据
      let dataPoints = 0;
      let sampleData: any = null;

      if (params.dataType === 'ai') {
        const performanceData = parsePerformanceData(data);
        dataPoints = performanceData.fund.length;
        sampleData = {
          fundPoints: performanceData.fund.slice(0, 3),
          benchmarkPoints: performanceData.benchmark.slice(0, 3),
          indexPoints: performanceData.index.slice(0, 3)
        };
      } else {
        const fundData = parseFundData(data);
        dataPoints = fundData.length;
        sampleData = fundData.slice(0, 3);
      }

      return {
        url,
        success: true,
        responseTime,
        dataPoints,
        sampleData
      };

    } catch (error) {
      const responseTime = Date.now() - startTime;
      return {
        url: buildApiUrl(params.fundCode, params),
        success: false,
        responseTime,
        dataPoints: 0,
        error: error instanceof Error ? error.message : '未知错误'
      };
    }
  }

  /**
   * 批量测试基金接口
   */
  async batchTestFund(fundCode: string): Promise<BatchTestResult> {
    console.log(`开始测试基金 ${fundCode} 的所有接口...`);

    const tests = {
      netValue: await this.testSingleApi({
        fundCode,
        dataType: 'nvl',
        months: 12
      }),
      performance3m: await this.testSingleApi({
        fundCode,
        dataType: 'ai',
        months: 3,
        source: 'qieman'
      }),
      performance6m: await this.testSingleApi({
        fundCode,
        dataType: 'ai',
        months: 6,
        source: 'qieman'
      }),
      performance12m: await this.testSingleApi({
        fundCode,
        dataType: 'ai',
        months: 12,
        source: 'qieman'
      }),
      longTerm: await this.testSingleApi({
        fundCode,
        dataType: 'nvl',
        months: 124
      })
    };

    const testResults = Object.values(tests);
    const successCount = testResults.filter(t => t.success).length;
    const totalResponseTime = testResults.reduce((sum, t) => sum + t.responseTime, 0);

    return {
      fundCode,
      tests,
      summary: {
        totalTests: testResults.length,
        successCount,
        failureCount: testResults.length - successCount,
        averageResponseTime: totalResponseTime / testResults.length
      }
    };
  }

  /**
   * 生成测试报告
   */
  generateReport(result: BatchTestResult): string {
    const { fundCode, tests, summary } = result;
    
    let report = `\n=== 基金 ${fundCode} API接口测试报告 ===\n\n`;
    
    report += `总体概况:\n`;
    report += `- 总测试数: ${summary.totalTests}\n`;
    report += `- 成功数: ${summary.successCount}\n`;
    report += `- 失败数: ${summary.failureCount}\n`;
    report += `- 成功率: ${((summary.successCount / summary.totalTests) * 100).toFixed(1)}%\n`;
    report += `- 平均响应时间: ${summary.averageResponseTime.toFixed(0)}ms\n\n`;

    report += `详细结果:\n`;
    
    Object.entries(tests).forEach(([testName, testResult]) => {
      report += `\n${testName}:\n`;
      report += `  状态: ${testResult.success ? '✅ 成功' : '❌ 失败'}\n`;
      report += `  响应时间: ${testResult.responseTime}ms\n`;
      report += `  数据点数: ${testResult.dataPoints}\n`;
      
      if (testResult.error) {
        report += `  错误: ${testResult.error}\n`;
      }
      
      if (testResult.sampleData) {
        report += `  示例数据: ${JSON.stringify(testResult.sampleData, null, 2).substring(0, 200)}...\n`;
      }
      
      report += `  URL: ${testResult.url}\n`;
    });

    return report;
  }

  /**
   * 设置请求超时时间
   */
  setTimeout(timeout: number): void {
    this.timeout = timeout;
  }
}

// 导出单例实例
export const apiTester = new ApiTester();

// 便捷函数
export async function testFundApi(fundCode: string): Promise<BatchTestResult> {
  return apiTester.batchTestFund(fundCode);
}

export async function quickTest(fundCode: string): Promise<void> {
  const result = await testFundApi(fundCode);
  const report = apiTester.generateReport(result);
  console.log(report);
}
