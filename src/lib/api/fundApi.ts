// 基金数据API服务

import type {
  BaiduApiResponse,
  DataFetchOptions,
  PerformanceComparisonData
} from './types';
import type { FundData } from '@/types/fund';
import {
  parseFundData,
  parsePerformanceData,
  buildApiUrl,
  validateFundCode as validateFundCodeFormat,
  validateApiParams,
  createApiError,
  getCacheItem,
  setCacheItem,
  withRetry,
  withTimeout,
  generateCacheKey,
  validateDateRange,
  formatErrorMessage
} from './utils';

/**
 * 基金API服务函数
 */

// 默认配置
const defaultOptions: Required<DataFetchOptions> = {
  useCache: true,
  timeout: 10000,
  retryCount: 3,
  retryDelay: 1000
};

/**
 * 获取基金历史净值数据
 */
export async function getFundData(
  fundCode: string,
  startDate?: string,
  endDate?: string,
  options: DataFetchOptions = {}
): Promise<FundData[]> {
  const opts = { ...defaultOptions, ...options };

  // 验证基金代码
  if (!validateFundCodeFormat(fundCode)) {
    throw createApiError('INVALID_FUND_CODE', `无效的基金代码: ${fundCode}`);
  }

  // 验证日期范围
  if (startDate && endDate && !validateDateRange(startDate, endDate)) {
    throw createApiError('INVALID_DATE_RANGE', '开始日期不能晚于结束日期');
  }

  // 检查缓存
  const cacheKey = generateCacheKey(fundCode, startDate, endDate);
  if (opts.useCache) {
    const cachedData = getCacheItem<FundData[]>(cacheKey);
    if (cachedData) {
      return cachedData;
    }
  }

  try {
    // 获取原始数据
    let fundData = await fetchRawData(fundCode, startDate, endDate, opts);

    // 应用日期过滤
    if (startDate || endDate) {
      fundData = filterByDateRange(fundData, startDate, endDate);
    }

    // 缓存结果
    if (opts.useCache) {
      setCacheItem(cacheKey, fundData);
    }

    return fundData;
  } catch (error) {
    const errorMessage = formatErrorMessage(error);
    throw createApiError('API_ERROR', `获取基金数据失败: ${errorMessage}`, error);
  }
}

/**
 * 获取基金基本信息
 */
export async function getFundInfo(fundCode: string): Promise<{ code: string; name: string } | null> {
  if (!validateFundCodeFormat(fundCode)) {
    return null;
  }

  try {
    // 在浏览器环境中，使用API路由
    if (typeof window !== 'undefined') {
      const response = await fetch(`/api/fund?code=${fundCode}`);
      if (response.ok) {
        const result = await response.json();
        if (result.success && result.data.length > 0) {
          return {
            code: fundCode,
            name: `基金${fundCode}` // 实际应该从API响应中提取
          };
        }
      }
      return null;
    }

    // 在服务端环境中，直接调用外部API
    const url = buildApiUrl(fundCode);
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache'
      }
    });

    if (!response.ok) {
      return null;
    }

    const data = await response.json();
    if (data.ResultCode !== '0') {
      return null;
    }

    // 从API响应中提取基金名称
    const result = data.Result?.[0];
    const query = result?.DisplayData?.resultData?.extData?.OriginQuery;

    if (query === fundCode) {
      return {
        code: fundCode,
        name: `基金${fundCode}` // 实际应该从API响应中提取
      };
    }

    return null;
  } catch (error) {
    console.warn(`获取基金${fundCode}信息失败:`, error);
    return null;
  }
}

/**
 * 验证基金代码是否存在
 */
export async function validateFundCode(fundCode: string): Promise<boolean> {
  try {
    const info = await getFundInfo(fundCode);
    return info !== null;
  } catch {
    return false;
  }
}

/**
 * 获取基金业绩对比数据
 */
export async function getPerformanceComparison(
  fundCode: string,
  months: number = 12,
  options?: DataFetchOptions
): Promise<PerformanceComparisonData> {
  // 验证参数
  const validation = validateApiParams({
    fundCode,
    dataType: 'ai',
    months,
    source: 'qieman'
  });

  if (!validation.valid) {
    throw createApiError('INVALID_PARAMS', validation.errors.join(', '));
  }

  const opts = { ...defaultOptions, ...options };
  const cacheKey = `performance_${fundCode}_${months}`;

  // 检查缓存
  if (opts.useCache) {
    const cached = getCacheItem<PerformanceComparisonData>(cacheKey);
    if (cached) {
      return cached;
    }
  }

  try {
    // 获取原始数据
    const performanceData = await fetchRawPerformanceData(fundCode, months, opts);

    // 缓存结果
    if (opts.useCache) {
      setCacheItem(cacheKey, performanceData);
    }

    return performanceData;
  } catch (error) {
    const errorMessage = formatErrorMessage(error);
    throw createApiError('API_ERROR', `获取业绩对比数据失败: ${errorMessage}`, error);
  }
}

/**
 * 获取原始API数据
 */
async function fetchRawData(
  fundCode: string,
  startDate?: string,
  endDate?: string,
  options: Required<DataFetchOptions> = defaultOptions
): Promise<FundData[]> {
  // 在浏览器环境中，使用Next.js API路由避免CORS问题
  if (typeof window !== 'undefined') {
    return fetchViaApiRoute(fundCode, startDate, endDate, options);
  }

  // 在服务端环境中，直接调用外部API
  const url = buildApiUrl(fundCode);

  const fetchWithRetry = () => withRetry(
    async () => {
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
          'Accept': 'application/json, text/plain, */*',
          'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.ResultCode !== '0') {
        throw new Error(`API错误: ${data.ResultCode}`);
      }

      return data as BaiduApiResponse;
    },
    { maxRetries: options.retryCount, delay: options.retryDelay }
  );

  const rawData = await withTimeout(fetchWithRetry(), options.timeout);
  const parsedData = parseFundData(rawData);

  // 转换为项目内部格式
  return parsedData.map(item => ({
    date: item.date,
    netAssetValue: item.netAssetValue,
    accumulatedValue: item.accumulatedValue,
    dailyGrowthRate: 0 // 计算日增长率或从其他字段获取
  }));
}

/**
 * 通过Next.js API路由获取数据（避免CORS问题）
 */
async function fetchViaApiRoute(
  fundCode: string,
  startDate?: string,
  endDate?: string,
  options?: Required<DataFetchOptions>
): Promise<FundData[]> {
  const params = new URLSearchParams({ code: fundCode });
  if (startDate) params.set('startDate', startDate);
  if (endDate) params.set('endDate', endDate);

  const fetchWithRetry = () => withRetry(
    async () => {
      const response = await fetch(`/api/fund?${params.toString()}`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json, text/plain, */*',
          'Cache-Control': 'no-cache',
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();

      if (!result.success) {
        throw new Error(`API错误: ${result.error}`);
      }

      return result.data as FundData[];
    },
    { maxRetries: options?.retryCount || 3, delay: options?.retryDelay || 1000 }
  );

  return withTimeout(fetchWithRetry(), options?.timeout || 10000);
}

/**
 * 获取业绩对比原始数据
 */
async function fetchRawPerformanceData(
  fundCode: string,
  months: number,
  options: Required<DataFetchOptions>
): Promise<any> {
  // 在浏览器环境中，使用Next.js API路由避免CORS问题
  if (typeof window !== 'undefined') {
    return fetchPerformanceViaApiRoute(fundCode, months, options);
  }

  // 在服务端环境中，直接调用外部API
  const url = buildApiUrl(fundCode, {
    dataType: 'ai',
    months,
    source: 'qieman'
  });

  const fetchWithRetry = () => withRetry(
    async () => {
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
          'Accept': 'application/json, text/plain, */*',
          'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.ResultCode !== '0') {
        throw new Error(`API错误: ${data.ResultCode}`);
      }

      return data as BaiduApiResponse;
    },
    { maxRetries: options.retryCount, delay: options.retryDelay }
  );

  const rawData = await withTimeout(fetchWithRetry(), options.timeout);
  return parsePerformanceData(rawData);
}

/**
 * 通过Next.js API路由获取业绩对比数据
 */
async function fetchPerformanceViaApiRoute(
  fundCode: string,
  months: number,
  options?: Required<DataFetchOptions>
): Promise<any> {
  const params = new URLSearchParams({
    code: fundCode,
    months: months.toString()
  });

  const fetchWithRetry = () => withRetry(
    async () => {
      const response = await fetch(`/api/fund/performance?${params.toString()}`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json, text/plain, */*',
          'Cache-Control': 'no-cache',
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();

      if (!result.success) {
        throw new Error(`API错误: ${result.error}`);
      }

      return result.data;
    },
    { maxRetries: options?.retryCount || 3, delay: options?.retryDelay || 1000 }
  );

  return withTimeout(fetchWithRetry(), options?.timeout || 10000);
}



/**
 * 按日期范围过滤数据
 */
function filterByDateRange(
  data: FundData[],
  startDate?: string,
  endDate?: string
): FundData[] {
  return data.filter(item => {
    const itemDate = new Date(item.date);

    if (startDate && itemDate < new Date(startDate)) {
      return false;
    }

    if (endDate && itemDate > new Date(endDate)) {
      return false;
    }

    return true;
  });
}

/**
 * 清除缓存
 */
export function clearFundCache(): void {
  // 使用全局缓存清除函数
  import('./utils').then(({ clearCache }) => clearCache());
}

/**
 * 获取缓存统计
 */
export function getFundCacheStats(): { size: number } {
  // 使用全局缓存统计函数
  import('./utils').then(({ getCacheSize }) => getCacheSize());
  return { size: 0 }; // 临时返回，实际应该是异步的
}

// 便捷函数别名
export async function fetchFundData(
  fundCode: string,
  startDate?: string,
  endDate?: string,
  options?: DataFetchOptions
): Promise<FundData[]> {
  return getFundData(fundCode, startDate, endDate, options);
}

export { validateFundCode as validateFund };
export { getFundInfo as getFundBasicInfo };
