import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { IndexApiService, fetchIndexData, getIndexBasicInfo } from '@/lib/api/indexApi';
import type { IndexData } from '@/types/fund';

// Mock fetch
const mockFetch = vi.fn();
global.fetch = mockFetch;

// Mock console to reduce noise
const mockConsole = {
  log: vi.fn(),
  warn: vi.fn(),
  error: vi.fn(),
};
global.console = mockConsole as any;

describe('IndexApiService (CSIndex)', () => {
  let apiClient: IndexApiService;

  beforeEach(() => {
    apiClient = new IndexApiService();
    apiClient.clearCache(); // 清除缓存
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe('getIndexData', () => {
    const mockCSIndexResponse = {
      code: '200',
      msg: 'Success',
      data: [
        {
          tradeDate: '20240101',
          indexCode: '000300',
          indexNameCnAll: '沪深300指数',
          indexNameCn: '沪深300',
          indexNameEnAll: 'CSI 300 Index',
          indexNameEn: 'CSI 300',
          open: 3500.00,
          high: 3550.00,
          low: 3480.00,
          close: 3520.00,
          change: 20.00,
          changePct: 0.57,
          tradingVol: 15000.00,
          tradingValue: 2500.00,
          consNumber: 300.0,
          peg: 12.5
        },
        {
          tradeDate: '20240102',
          indexCode: '000300',
          indexNameCnAll: '沪深300指数',
          indexNameCn: '沪深300',
          indexNameEnAll: 'CSI 300 Index',
          indexNameEn: 'CSI 300',
          open: 3520.00,
          high: 3580.00,
          low: 3510.00,
          close: 3560.00,
          change: 40.00,
          changePct: 1.14,
          tradingVol: 16000.00,
          tradingValue: 2800.00,
          consNumber: 300.0,
          peg: 12.8
        }
      ]
    };

    it('应该成功获取指数数据', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockCSIndexResponse),
      });

      const result = await apiClient.getIndexData('000300', '2024-01-01', '2024-01-02');

      expect(result).toBeDefined();
      expect(Array.isArray(result)).toBe(true);
      expect(result.length).toBe(2);
      expect(result[0]).toHaveProperty('date');
      expect(result[0]).toHaveProperty('value');
      expect(result[0].date).toBe('2024-01-01');
      expect(result[0].value).toBe(3520.00);
    });

    it('应该验证指数代码格式', async () => {
      await expect(
        apiClient.getIndexData('invalid', '2024-01-01', '2024-01-02')
      ).rejects.toThrow('无效的指数代码');
    });

    it('应该验证日期范围', async () => {
      await expect(
        apiClient.getIndexData('000300', '2024-12-31', '2024-01-01')
      ).rejects.toThrow('开始日期不能晚于结束日期');
    });

    it('应该处理API错误响应', async () => {
      const errorResponse = {
        code: '500',
        msg: '服务器错误',
        data: null,
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(errorResponse),
      });

      await expect(
        apiClient.getIndexData('000300', '2024-01-01', '2024-01-02')
      ).rejects.toThrow('API错误');
    }, 10000);

    it('应该处理网络错误', async () => {
      mockFetch.mockRejectedValueOnce(new Error('Network error'));

      await expect(
        apiClient.getIndexData('000300', '2024-01-01', '2024-01-02')
      ).rejects.toThrow();
    }, 10000);

    it('应该处理HTTP错误状态', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 404,
        statusText: 'Not Found',
      });

      await expect(
        apiClient.getIndexData('000300', '2024-01-01', '2024-01-02')
      ).rejects.toThrow('HTTP 404');
    }, 10000);
  });

  describe('getIndexInfo', () => {
    it('应该成功获取指数基本信息', async () => {
      const result = await apiClient.getIndexInfo('000300');

      expect(result).toBeDefined();
      expect(result?.code).toBe('000300');
      expect(result?.name).toBe('沪深300');
    });

    it('应该验证指数代码', async () => {
      const result = await apiClient.getIndexInfo('invalid');
      expect(result).toBeNull();
    });

    it('应该返回正确的指数名称', async () => {
      const testCases = [
        { code: '000001', name: '上证综指' },
        { code: '000300', name: '沪深300' },
        { code: '000905', name: '中证500' },
        { code: '399006', name: '创业板指' },
      ];

      for (const testCase of testCases) {
        const result = await apiClient.getIndexInfo(testCase.code);
        expect(result?.name).toBe(testCase.name);
      }
    });
  });

  describe('缓存功能', () => {
    const mockCSIndexResponse = {
      code: '200',
      msg: 'Success',
      data: [
        {
          tradeDate: '20240101',
          indexCode: '000300',
          indexNameCnAll: '沪深300指数',
          indexNameCn: '沪深300',
          indexNameEnAll: 'CSI 300 Index',
          indexNameEn: 'CSI 300',
          open: 3500.00,
          high: 3550.00,
          low: 3480.00,
          close: 3520.00,
          change: 20.00,
          changePct: 0.57,
          tradingVol: 15000.00,
          tradingValue: 2500.00,
          consNumber: 300.0,
          peg: 12.5
        }
      ]
    };

    it('应该缓存API响应', async () => {
      mockFetch.mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockCSIndexResponse),
      });

      // 第一次调用
      await apiClient.getIndexData('000300', '2024-01-01', '2024-01-02');
      expect(mockFetch).toHaveBeenCalledTimes(1);

      // 第二次调用应该使用缓存
      await apiClient.getIndexData('000300', '2024-01-01', '2024-01-02');
      expect(mockFetch).toHaveBeenCalledTimes(1); // 仍然是1次，说明使用了缓存
    });

    it('应该支持禁用缓存', async () => {
      mockFetch.mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockCSIndexResponse),
      });

      // 第一次调用
      await apiClient.getIndexData('000300', '2024-01-01', '2024-01-02', { useCache: false });
      expect(mockFetch).toHaveBeenCalledTimes(1);

      // 第二次调用不使用缓存
      await apiClient.getIndexData('000300', '2024-01-01', '2024-01-02', { useCache: false });
      expect(mockFetch).toHaveBeenCalledTimes(2);
    });
  });

  describe('数据转换', () => {
    it('应该正确转换中证API响应数据', async () => {
      const mockCSIndexResponse = {
        code: '200',
        msg: 'Success',
        data: [
          {
            tradeDate: '20240101',
            indexCode: '000300',
            indexNameCnAll: '沪深300指数',
            indexNameCn: '沪深300',
            indexNameEnAll: 'CSI 300 Index',
            indexNameEn: 'CSI 300',
            open: 3500.00,
            high: 3550.00,
            low: 3480.00,
            close: 3520.00,
            change: 20.00,
            changePct: 0.57,
            tradingVol: 15000.00,
            tradingValue: 2500.00,
            consNumber: 300.0,
            peg: 12.5
          }
        ]
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockCSIndexResponse),
      });

      const result = await apiClient.getIndexData('000300', '2024-01-01', '2024-01-02');

      expect(result[0].value).toBe(3520.00);
      expect(result[0].date).toBe('2024-01-01');
      expect(result[0].volume).toBe(150000000); // 15000 * 10000
      expect(typeof result[0].value).toBe('number');
    });
  });

  describe('边界情况', () => {
    it('应该处理空的API响应', async () => {
      const mockCSIndexResponse = {
        code: '200',
        msg: 'Success',
        data: []
      };

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockCSIndexResponse),
      });

      const result = await apiClient.getIndexData('000300', '2024-01-01', '2024-01-02');

      expect(result).toBeDefined();
      expect(Array.isArray(result)).toBe(true);
      expect(result.length).toBe(0);
    });
  });
});

describe('便捷函数', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('fetchIndexData 应该正常工作', async () => {
    const mockCSIndexResponse = {
      code: '200',
      msg: 'Success',
      data: [
        {
          tradeDate: '20240101',
          indexCode: '000300',
          indexNameCnAll: '沪深300指数',
          indexNameCn: '沪深300',
          indexNameEnAll: 'CSI 300 Index',
          indexNameEn: 'CSI 300',
          open: 3500.00,
          high: 3550.00,
          low: 3480.00,
          close: 3520.00,
          change: 20.00,
          changePct: 0.57,
          tradingVol: 15000.00,
          tradingValue: 2500.00,
          consNumber: 300.0,
          peg: 12.5
        }
      ]
    };

    mockFetch.mockResolvedValueOnce({
      ok: true,
      json: () => Promise.resolve(mockCSIndexResponse),
    });

    const result = await fetchIndexData('000300', '2024-01-01', '2024-01-02');
    expect(result).toBeDefined();
    expect(result.length).toBe(1);
  });

  it('getIndexBasicInfo 应该正常工作', async () => {
    const result = await getIndexBasicInfo('000300');
    expect(result).toBeDefined();
    expect(result?.code).toBe('000300');
  });
});
