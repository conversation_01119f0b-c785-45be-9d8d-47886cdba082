'use server';

import { getFunds, searchFunds, validateFund } from '@/lib/mockData';
import type { Fund } from '@/types/fund';

/**
 * Server action to get all funds
 */
export async function getFundsAction(): Promise<Fund[]> {
  try {
    return await getFunds();
  } catch (error) {
    console.error('Failed to load funds:', error);
    throw new Error('Failed to load funds');
  }
}

/**
 * Server action to search funds
 */
export async function searchFundsAction(query: string): Promise<Fund[]> {
  try {
    if (!query.trim()) {
      return [];
    }
    return await searchFunds(query);
  } catch (error) {
    console.error('Failed to search funds:', error);
    throw new Error('Failed to search funds');
  }
}

/**
 * Server action to validate fund code
 */
export async function validateFundAction(fundCode: string): Promise<boolean> {
  try {
    if (!fundCode.match(/^\d{6}$/)) {
      return false;
    }
    return await validateFund(fundCode);
  } catch (error) {
    console.error('Failed to validate fund:', error);
    return false;
  }
}
