import type { Strategy, StrategyType } from "@/types/fund";

// 投资策略配置
export const strategies: Record<StrategyType, Strategy> = {
  fixed_amount: {
    id: "fixed_amount",
    name: "定期定额投资",
    description: "每隔固定时间投入固定金额，适合长期投资和分散风险",
    riskLevel: "low",
    complexity: "beginner",
    parameterSchema: {
      startDate: {
        type: "date",
        label: "开始日期",
        required: true,
        defaultValue: "2020-01-01",
        description: "投资开始的日期",
      },
      endDate: {
        type: "date",
        label: "结束日期",
        required: true,
        defaultValue: "2024-01-01",
        description: "投资结束的日期",
      },
      initialAmount: {
        type: "number",
        label: "初始投资金额 (元)",
        required: true,
        min: 0,
        step: 100,
        defaultValue: 10000,
        description: "首次投资的金额",
      },
      monthlyAmount: {
        type: "number",
        label: "定投金额 (元)",
        required: true,
        min: 100,
        step: 100,
        defaultValue: 1000,
        description: "每期投资的固定金额",
      },
      frequency: {
        type: "select",
        label: "投资频率",
        required: true,
        options: [
          { value: "weekly", label: "每周" },
          { value: "monthly", label: "每月" },
          { value: "quarterly", label: "每季度" },
        ],
        defaultValue: "monthly",
        description: "投资的时间间隔",
      },
    },
  },

  value_averaging: {
    id: "value_averaging",
    name: "价值平均策略",
    description: "根据目标增长率调整投资金额，市场低迷时多投，高涨时少投",
    riskLevel: "medium",
    complexity: "intermediate",
    parameterSchema: {
      startDate: {
        type: "date",
        label: "开始日期",
        required: true,
        defaultValue: "2020-01-01",
      },
      endDate: {
        type: "date",
        label: "结束日期",
        required: true,
        defaultValue: "2024-01-01",
      },
      initialAmount: {
        type: "number",
        label: "初始投资金额 (元)",
        required: true,
        min: 0,
        step: 100,
        defaultValue: 10000,
      },
      targetGrowthRate: {
        type: "number",
        label: "目标年化增长率 (%)",
        required: true,
        min: 1,
        max: 30,
        step: 0.5,
        defaultValue: 8,
        description: "期望的年化收益率",
      },
      maxInvestment: {
        type: "number",
        label: "单次最大投资额 (元)",
        required: true,
        min: 1000,
        step: 500,
        defaultValue: 5000,
        description: "单次投资的上限金额",
      },
      frequency: {
        type: "select",
        label: "调整频率",
        required: true,
        options: [
          { value: "monthly", label: "每月" },
          { value: "quarterly", label: "每季度" },
        ],
        defaultValue: "monthly",
      },
    },
  },

  smart_fixed: {
    id: "smart_fixed",
    name: "智能定投",
    description: "根据市场估值调整投资金额，估值低时多投，估值高时少投",
    riskLevel: "medium",
    complexity: "intermediate",
    parameterSchema: {
      startDate: {
        type: "date",
        label: "开始日期",
        required: true,
        defaultValue: "2020-01-01",
      },
      endDate: {
        type: "date",
        label: "结束日期",
        required: true,
        defaultValue: "2024-01-01",
      },
      initialAmount: {
        type: "number",
        label: "初始投资金额 (元)",
        required: true,
        min: 0,
        step: 100,
        defaultValue: 10000,
      },
      baseAmount: {
        type: "number",
        label: "基础投资额 (元)",
        required: true,
        min: 100,
        step: 100,
        defaultValue: 1000,
        description: "正常情况下的投资金额",
      },
      adjustmentFactor: {
        type: "number",
        label: "调整系数",
        required: true,
        min: 0.1,
        max: 3,
        step: 0.1,
        defaultValue: 1.5,
        description: "根据估值调整投资金额的系数",
      },
      valuationMetric: {
        type: "select",
        label: "估值指标",
        required: true,
        options: [
          { value: "pe", label: "市盈率 (PE)" },
          { value: "pb", label: "市净率 (PB)" },
          { value: "rsi", label: "RSI指标" },
        ],
        defaultValue: "pe",
        description: "用于判断市场估值的指标",
      },
      frequency: {
        type: "select",
        label: "投资频率",
        required: true,
        options: [{ value: "monthly", label: "每月" }],
        defaultValue: "monthly",
      },
    },
  },

  grid_trading: {
    id: "grid_trading",
    name: "网格交易",
    description: "在价格区间内设置多个买卖点，低买高卖获取价差收益",
    riskLevel: "high",
    complexity: "advanced",
    parameterSchema: {
      startDate: {
        type: "date",
        label: "开始日期",
        required: true,
        defaultValue: "2020-01-01",
      },
      endDate: {
        type: "date",
        label: "结束日期",
        required: true,
        defaultValue: "2024-01-01",
      },
      initialAmount: {
        type: "number",
        label: "初始投资金额 (元)",
        required: true,
        min: 0,
        step: 100,
        defaultValue: 50000,
        description: "用于网格交易的总资金",
      },
      gridCount: {
        type: "number",
        label: "网格数量",
        required: true,
        min: 5,
        max: 50,
        step: 1,
        defaultValue: 10,
        description: "在价格区间内设置的网格数量",
      },
      priceRangeMin: {
        type: "number",
        label: "价格区间下限",
        required: true,
        min: 0.1,
        step: 0.01,
        defaultValue: 1.0,
        description: "网格交易的最低价格",
      },
      priceRangeMax: {
        type: "number",
        label: "价格区间上限",
        required: true,
        min: 0.1,
        step: 0.01,
        defaultValue: 3.0,
        description: "网格交易的最高价格",
      },
      investmentPerGrid: {
        type: "number",
        label: "每格投资额 (元)",
        required: true,
        min: 500,
        step: 100,
        defaultValue: 2000,
        description: "每个网格的投资金额",
      },
      rebalanceFrequency: {
        type: "select",
        label: "调仓频率",
        required: true,
        options: [
          { value: "daily", label: "每日" },
          { value: "weekly", label: "每周" },
          { value: "monthly", label: "每月" }
        ],
        defaultValue: "weekly",
        description: "检查和调整网格的频率",
      },
      stopLoss: {
        type: "number",
        label: "止损比例 (%)",
        required: false,
        min: 1,
        max: 50,
        step: 1,
        defaultValue: 20,
        description: "可选的止损设置",
      },
      takeProfit: {
        type: "number",
        label: "止盈比例 (%)",
        required: false,
        min: 5,
        max: 100,
        step: 1,
        defaultValue: 50,
        description: "可选的止盈设置",
      },
    },
  },

  momentum: {
    id: "momentum",
    name: "动量策略",
    description: "追踪价格趋势，在上涨趋势中增加投资，下跌趋势中减少投资",
    riskLevel: "high",
    complexity: "advanced",
    parameterSchema: {
      startDate: {
        type: "date",
        label: "开始日期",
        required: true,
        defaultValue: "2020-01-01",
      },
      endDate: {
        type: "date",
        label: "结束日期",
        required: true,
        defaultValue: "2024-01-01",
      },
      initialAmount: {
        type: "number",
        label: "初始投资金额 (元)",
        required: true,
        min: 0,
        step: 100,
        defaultValue: 10000,
      },
      lookbackPeriod: {
        type: "number",
        label: "回看期间 (天)",
        required: true,
        min: 5,
        max: 252,
        step: 1,
        defaultValue: 20,
        description: "计算动量的历史期间",
      },
      threshold: {
        type: "number",
        label: "动量阈值 (%)",
        required: true,
        min: 1,
        max: 20,
        step: 0.5,
        defaultValue: 5,
        description: "触发买卖信号的动量阈值",
      },
      rebalanceFrequency: {
        type: "select",
        label: "调仓频率",
        required: true,
        options: [
          { value: "monthly", label: "每月" },
          { value: "quarterly", label: "每季度" },
        ],
        defaultValue: "monthly",
      },
    },
  },

  mean_reversion: {
    id: "mean_reversion",
    name: "均值回归策略",
    description: "基于价格偏离均值的程度进行投资，价格低于均值时买入",
    riskLevel: "medium",
    complexity: "intermediate",
    parameterSchema: {
      startDate: {
        type: "date",
        label: "开始日期",
        required: true,
        defaultValue: "2020-01-01",
      },
      endDate: {
        type: "date",
        label: "结束日期",
        required: true,
        defaultValue: "2024-01-01",
      },
      initialAmount: {
        type: "number",
        label: "初始投资金额 (元)",
        required: true,
        min: 0,
        step: 100,
        defaultValue: 10000,
      },
      movingAveragePeriod: {
        type: "number",
        label: "移动平均期间 (天)",
        required: true,
        min: 5,
        max: 252,
        step: 1,
        defaultValue: 50,
        description: "计算移动平均的期间",
      },
      deviationThreshold: {
        type: "number",
        label: "偏离阈值 (%)",
        required: true,
        min: 1,
        max: 30,
        step: 1,
        defaultValue: 10,
        description: "触发买卖信号的偏离程度",
      },
      rebalanceFrequency: {
        type: "select",
        label: "调仓频率",
        required: true,
        options: [
          { value: "weekly", label: "每周" },
          { value: "monthly", label: "每月" },
        ],
        defaultValue: "monthly",
      },
    },
  },
};

// 获取策略列表
export function getStrategies(): Strategy[] {
  return Object.values(strategies);
}

// 根据ID获取策略
export function getStrategy(id: StrategyType): Strategy | undefined {
  return strategies[id];
}
