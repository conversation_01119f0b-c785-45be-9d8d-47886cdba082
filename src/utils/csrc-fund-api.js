/**
 * CSRC Fund API Utility
 * Provides functions to fetch fund data from China Securities Regulatory Commission
 */

const https = require('https');
const http = require('http');

/**
 * Build aoData parameter for the API request
 * @param {Object} params - Parameters to override defaults
 * @returns {string} - Encoded aoData parameter
 */
function buildAoData(params = {}) {
  const defaultParams = {
    sEcho: 1,
    iColumns: 5,
    sColumns: ',,,',
    iDisplayStart: 0,
    iDisplayLength: 20,
    mDataProp_0: 'fund',
    mDataProp_1: 'fund', 
    mDataProp_2: 'fund',
    mDataProp_3: 'fund',
    mDataProp_4: 'valuationDate',
    fundType: 'all',
    fundCompanyShortName: '',
    fundCode: '',
    fundName: '',
    startDate: new Date().toISOString().split('T')[0],
    endDate: new Date().toISOString().split('T')[0]
  };

  const mergedParams = { ...defaultParams, ...params };
  
  const aoDataArray = Object.entries(mergedParams).map(([name, value]) => ({
    name,
    value
  }));

  return encodeURIComponent(JSON.stringify(aoDataArray));
}

/**
 * Make HTTP request
 * @param {string} url - URL to request
 * @returns {Promise<Object>} - Parsed JSON response
 */
function makeRequest(url) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const options = {
      hostname: urlObj.hostname,
      port: urlObj.port || (urlObj.protocol === 'https:' ? 443 : 80),
      path: urlObj.pathname + urlObj.search,
      method: 'GET',
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'application/json, text/javascript, */*; q=0.01',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Referer': 'http://eid.csrc.gov.cn/fund/disclose/publicFundJZInfo.do'
      }
    };

    const client = urlObj.protocol === 'https:' ? https : http;
    
    const req = client.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve(jsonData);
        } catch (error) {
          reject(new Error(`Failed to parse JSON: ${error.message}`));
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.setTimeout(10000, () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    req.end();
  });
}

/**
 * Fetch fund data from CSRC API
 * @param {Object} params - Query parameters
 * @param {number} params.iDisplayStart - Starting record index (for pagination)
 * @param {number} params.iDisplayLength - Number of records to return
 * @param {string} params.fundCode - Fund code to search for
 * @param {string} params.fundName - Fund name to search for (partial match)
 * @param {string} params.fundCompanyShortName - Fund company name
 * @param {string} params.startDate - Start date (YYYY-MM-DD)
 * @param {string} params.endDate - End date (YYYY-MM-DD)
 * @param {string} params.fundType - Fund type ('all' or specific type)
 * @returns {Promise<Object>} - API response with fund data
 */
async function fetchFundData(params = {}) {
  const baseUrl = 'http://eid.csrc.gov.cn/fund/disclose/getPublicFundJZInfoMore.do';
  const aoData = buildAoData(params);
  const timestamp = Date.now();
  const url = `${baseUrl}?aoData=${aoData}&_=${timestamp}`;
  
  try {
    const data = await makeRequest(url);
    return data;
  } catch (error) {
    console.error('Error fetching fund data:', error.message);
    throw error;
  }
}

/**
 * Search funds by name (partial match)
 * @param {string} fundName - Fund name to search for
 * @param {number} limit - Maximum number of results (default: 50)
 * @returns {Promise<Array>} - Array of matching funds
 */
async function searchFundsByName(fundName, limit = 50) {
  const data = await fetchFundData({
    fundName,
    iDisplayLength: limit
  });
  
  return data.aaData.map(fund => ({
    code: fund.code,
    name: fund.shortName,
    shareNetValue: fund.shareNetValue,
    totalNetValue: fund.totalNetValue,
    valuationDate: fund.valuationDate
  }));
}

/**
 * Get fund by specific code
 * @param {string} fundCode - Fund code
 * @param {string} date - Date (YYYY-MM-DD), defaults to today
 * @returns {Promise<Object|null>} - Fund data or null if not found
 */
async function getFundByCode(fundCode, date = null) {
  const searchDate = date || new Date().toISOString().split('T')[0];
  
  const data = await fetchFundData({
    fundCode,
    startDate: searchDate,
    endDate: searchDate,
    iDisplayLength: 1
  });
  
  if (data.aaData.length === 0) {
    return null;
  }
  
  const fund = data.aaData[0];
  return {
    code: fund.code,
    name: fund.shortName,
    shareNetValue: fund.shareNetValue,
    totalNetValue: fund.totalNetValue,
    valuationDate: fund.valuationDate,
    classification: fund.classification,
    fund: fund.fund
  };
}

/**
 * Get all funds with pagination
 * @param {number} page - Page number (0-based)
 * @param {number} pageSize - Number of records per page
 * @param {string} date - Date (YYYY-MM-DD), defaults to today
 * @returns {Promise<Object>} - Paginated fund data
 */
async function getAllFunds(page = 0, pageSize = 100, date = null) {
  const searchDate = date || new Date().toISOString().split('T')[0];
  
  const data = await fetchFundData({
    iDisplayStart: page * pageSize,
    iDisplayLength: pageSize,
    startDate: searchDate,
    endDate: searchDate
  });
  
  return {
    totalRecords: data.iTotalRecords,
    currentPage: page,
    pageSize: pageSize,
    totalPages: Math.ceil(data.iTotalRecords / pageSize),
    funds: data.aaData.map(fund => ({
      code: fund.code,
      name: fund.shortName,
      shareNetValue: fund.shareNetValue,
      totalNetValue: fund.totalNetValue,
      valuationDate: fund.valuationDate
    }))
  };
}

/**
 * Get funds by company name
 * @param {string} companyName - Fund company name
 * @param {number} limit - Maximum number of results
 * @returns {Promise<Array>} - Array of funds from the company
 */
async function getFundsByCompany(companyName, limit = 50) {
  const data = await fetchFundData({
    fundCompanyShortName: companyName,
    iDisplayLength: limit
  });
  
  return data.aaData.map(fund => ({
    code: fund.code,
    name: fund.shortName,
    shareNetValue: fund.shareNetValue,
    totalNetValue: fund.totalNetValue,
    valuationDate: fund.valuationDate
  }));
}

module.exports = {
  fetchFundData,
  searchFundsByName,
  getFundByCode,
  getAllFunds,
  getFundsByCompany,
  buildAoData
};
